import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from PIL import Image, ImageTk, ImageFilter, ImageOps
import os
import threading
from pathlib import Path

class OutlineExtractor:
    def __init__(self, root):
        self.root = root
        self.root.title("Image Outline Extractor")
        self.root.geometry("800x600")
        
        self.selected_files = []
        self.output_folder = ""
        self.processing = False
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # File selection section
        ttk.Label(main_frame, text="Select Images:").grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        file_frame = ttk.Frame(main_frame)
        file_frame.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 5))
        file_frame.columnconfigure(0, weight=1)
        
        ttk.Button(file_frame, text="Browse Files", command=self.browse_files).grid(row=0, column=0, sticky=tk.W)
        ttk.Button(file_frame, text="Browse Folder", command=self.browse_folder).grid(row=0, column=1, sticky=tk.W, padx=(10, 0))
        
        # Output folder section
        ttk.Label(main_frame, text="Output Folder:").grid(row=1, column=0, sticky=tk.W, pady=(10, 5))
        
        output_frame = ttk.Frame(main_frame)
        output_frame.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(10, 5))
        output_frame.columnconfigure(0, weight=1)
        
        self.output_label = ttk.Label(output_frame, text="No folder selected", foreground="gray")
        self.output_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        ttk.Button(output_frame, text="Browse", command=self.browse_output_folder).grid(row=0, column=1, sticky=tk.E, padx=(10, 0))
        
        # File list
        list_frame = ttk.Frame(main_frame)
        list_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # Listbox with scrollbar
        self.file_listbox = tk.Listbox(list_frame, selectmode=tk.EXTENDED)
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.file_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Processing options
        options_frame = ttk.LabelFrame(main_frame, text="Processing Options", padding="10")
        options_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Threshold slider
        ttk.Label(options_frame, text="Outline Threshold:").grid(row=0, column=0, sticky=tk.W)
        self.threshold_var = tk.IntVar(value=128)
        self.threshold_scale = ttk.Scale(options_frame, from_=50, to=200, orient=tk.HORIZONTAL, 
                                       variable=self.threshold_var, length=200)
        self.threshold_scale.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(10, 0))
        self.threshold_label = ttk.Label(options_frame, text="128")
        self.threshold_label.grid(row=0, column=2, padx=(5, 0))
        
        # Update threshold label
        self.threshold_var.trace_add('write', self.update_threshold_label)
        
        # Edge enhancement
        self.enhance_edges_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Enhance edges", variable=self.enhance_edges_var).grid(row=1, column=0, sticky=tk.W, pady=(5, 0))
        
        # Smooth lines
        self.smooth_lines_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Smooth lines", variable=self.smooth_lines_var).grid(row=1, column=1, sticky=tk.W, pady=(5, 0))
        
        # Transparent background option
        self.transparent_bg_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(options_frame, text="Transparent background", variable=self.transparent_bg_var).grid(row=2, column=0, sticky=tk.W, pady=(5, 0))
        
        # Output format
        ttk.Label(options_frame, text="Output format:").grid(row=2, column=1, sticky=tk.W, pady=(5, 0))
        self.format_var = tk.StringVar(value="PNG")
        format_combo = ttk.Combobox(options_frame, textvariable=self.format_var, values=["PNG", "SVG-style PNG"], 
                                   state="readonly", width=15)
        format_combo.grid(row=2, column=2, sticky=tk.W, pady=(5, 0), padx=(5, 0))
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=4, column=0, columnspan=2, pady=(20, 0))
        
        self.process_button = ttk.Button(button_frame, text="Process Images", command=self.start_processing)
        self.process_button.grid(row=0, column=0, padx=(0, 10))
        
        ttk.Button(button_frame, text="Clear List", command=self.clear_list).grid(row=0, column=1, padx=(0, 10))
        ttk.Button(button_frame, text="Remove Selected", command=self.remove_selected).grid(row=0, column=2)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(main_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Status label
        self.status_label = ttk.Label(main_frame, text="Ready")
        self.status_label.grid(row=6, column=0, columnspan=2, pady=(5, 0))
        
    def update_threshold_label(self, *args):
        self.threshold_label.config(text=str(self.threshold_var.get()))
        
    def browse_files(self):
        files = filedialog.askopenfilenames(
            title="Select Images",
            filetypes=[
                ("Image files", "*.png *.jpg *.jpeg *.gif *.bmp *.tiff *.webp"),
                ("PNG files", "*.png"),
                ("JPEG files", "*.jpg *.jpeg"),
                ("All files", "*.*")
            ]
        )
        if files:
            self.selected_files.extend(files)
            self.update_file_list()
            
    def browse_folder(self):
        folder = filedialog.askdirectory(title="Select Folder Containing Images")
        if folder:
            # Find all image files in the folder
            image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp'}
            folder_path = Path(folder)
            image_files = [
                str(file) for file in folder_path.iterdir()
                if file.suffix.lower() in image_extensions
            ]
            
            if image_files:
                self.selected_files.extend(image_files)
                self.update_file_list()
                messagebox.showinfo("Success", f"Found {len(image_files)} image files in the folder.")
            else:
                messagebox.showwarning("Warning", "No image files found in the selected folder.")
                
    def browse_output_folder(self):
        folder = filedialog.askdirectory(title="Select Output Folder")
        if folder:
            self.output_folder = folder
            self.output_label.config(text=folder, foreground="black")
            
    def update_file_list(self):
        self.file_listbox.delete(0, tk.END)
        for file in self.selected_files:
            self.file_listbox.insert(tk.END, os.path.basename(file))
            
    def clear_list(self):
        self.selected_files.clear()
        self.update_file_list()
        
    def remove_selected(self):
        selected_indices = self.file_listbox.curselection()
        if selected_indices:
            # Remove in reverse order to maintain indices
            for index in reversed(selected_indices):
                del self.selected_files[index]
            self.update_file_list()
            
    def process_image(self, input_path, output_path):
        """Process a single image to extract clean black outlines"""
        try:
            # Open image
            img = Image.open(input_path)
            
            # Convert to RGBA if not already
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # Convert to grayscale for processing
            gray = img.convert('L')
            
            # Apply threshold to separate icon from background
            threshold = self.threshold_var.get()
            
            # Create a binary mask - dark pixels become the icon
            def threshold_func(x):
                return 0 if x < threshold else 255
            
            binary = gray.point(threshold_func, mode='1')
            
            # Smooth lines if requested
            if self.smooth_lines_var.get():
                binary = binary.filter(ImageFilter.SMOOTH_MORE)
            
            # Enhance edges if requested
            if self.enhance_edges_var.get():
                binary = binary.filter(ImageFilter.EDGE_ENHANCE)
            
            # Create final image with transparent background
            result = Image.new('RGBA', img.size, (0, 0, 0, 0))
            
            # Convert binary to RGBA
            binary_rgba = binary.convert('RGBA')
            
            # Process pixels - keep dark areas as black icons, make light areas transparent
            data = binary_rgba.getdata()
            new_data = []
            for item in data:
                if item[0] < 128:  # Dark pixel (the icon itself)
                    new_data.append((0, 0, 0, 255))  # Solid black icon
                else:  # Light pixel (background)
                    if self.transparent_bg_var.get():
                        new_data.append((0, 0, 0, 0))  # Fully transparent
                    else:
                        new_data.append((255, 255, 255, 255))  # White background
            
            result.putdata(new_data)
            
            # Save the result
            result.save(output_path, 'PNG')
            return True
            
        except Exception as e:
            print(f"Error processing {input_path}: {str(e)}")
            return False
            
    def start_processing(self):
        if not self.selected_files:
            messagebox.showwarning("Warning", "Please select images to process.")
            return
            
        if not self.output_folder:
            messagebox.showwarning("Warning", "Please select an output folder.")
            return
            
        # Start processing in a separate thread
        self.processing = True
        self.process_button.config(text="Processing...", state="disabled")
        
        thread = threading.Thread(target=self.process_images)
        thread.daemon = True
        thread.start()
        
    def process_images(self):
        total_files = len(self.selected_files)
        processed = 0
        failed = 0
        
        for i, input_path in enumerate(self.selected_files):
            # Update status
            filename = os.path.basename(input_path)
            self.root.after(0, lambda f=filename: self.status_label.config(text=f"Processing: {f}"))
            
            # Create output path
            base_name = os.path.splitext(filename)[0]
            output_path = os.path.join(self.output_folder, f"{base_name}_outline.png")
            
            # Process the image
            if self.process_image(input_path, output_path):
                processed += 1
            else:
                failed += 1
                
            # Update progress
            progress = ((i + 1) / total_files) * 100
            self.root.after(0, lambda p=progress: self.progress_var.set(p))
            
        # Processing complete
        self.processing = False
        self.root.after(0, self.processing_complete, processed, failed)
        
    def processing_complete(self, processed, failed):
        self.process_button.config(text="Process Images", state="normal")
        self.progress_var.set(0)
        
        message = f"Processing complete!\n\nProcessed: {processed} files"
        if failed > 0:
            message += f"\nFailed: {failed} files"
            
        self.status_label.config(text="Ready")
        messagebox.showinfo("Complete", message)

def main():
    root = tk.Tk()
    app = OutlineExtractor(root)
    root.mainloop()

if __name__ == "__main__":
    main()